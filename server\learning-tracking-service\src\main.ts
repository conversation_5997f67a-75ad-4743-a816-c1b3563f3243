/**
 * 学习记录跟踪服务启动文件
 * 提供xAPI数据采集、用户画像分析和个性化推荐功能
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('LearningTrackingService');
  
  try {
    logger.log('🚀 启动学习记录跟踪服务...');

    // 创建应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn', 'debug'],
    });

    const configService = app.get(ConfigService);

    // 全局验证管道
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      disableErrorMessages: configService.get('NODE_ENV') === 'production',
    }));

    // 启用CORS
    app.enableCors({
      origin: configService.get('CORS_ORIGIN', '*'),
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      allowedHeaders: 'Content-Type, Accept, Authorization, X-Requested-With',
      credentials: true,
    });

    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // Swagger文档配置
    if (configService.get('NODE_ENV') !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('学习记录跟踪服务 API')
        .setDescription('基于xAPI的学习数据采集、用户画像分析和个性化推荐服务')
        .setVersion('1.0')
        .addTag('learning-tracking', '学习记录跟踪')
        .addTag('xapi', 'xAPI数据采集')
        .addTag('profile', '用户画像分析')
        .addTag('recommendation', '个性化推荐')
        .addTag('sync', '数据同步')
        .addBearerAuth()
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
        },
      });
    }

    // 微服务配置
    const microservicePort = configService.get<number>('MICROSERVICE_PORT', 3031);
    const microserviceOptions: MicroserviceOptions = {
      transport: Transport.TCP,
      options: {
        host: configService.get<string>('MICROSERVICE_HOST', '0.0.0.0'),
        port: microservicePort,
      },
    };

    // 连接微服务
    app.connectMicroservice(microserviceOptions);
    await app.startAllMicroservices();
    logger.log(`微服务已启动在端口: ${microservicePort}`);

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3030);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`🚀 学习记录跟踪服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`📖 API文档地址: http://${host}:${port}/api/docs`);
    logger.log(`🔍 健康检查: http://${host}:${port}/api/v1/health`);
    logger.log(`🔧 环境: ${configService.get<string>('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('启动失败:', error);
    process.exit(1);
  }
}

bootstrap();
